{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T02:59:07.426Z", "updatedAt": "2025-08-04T02:59:07.447Z", "resourceCount": 44}, "resources": [{"id": "banner", "source": "project", "protocol": "role", "name": "Banner 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/banner/banner.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.427Z", "updatedAt": "2025-08-04T02:59:07.427Z", "scannedAt": "2025-08-04T02:59:07.427Z", "path": "role/banner/banner.role.md"}}, {"id": "ears-specification", "source": "project", "protocol": "execution", "name": "Ears Specification 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/banner/execution/ears-specification.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.428Z", "updatedAt": "2025-08-04T02:59:07.428Z", "scannedAt": "2025-08-04T02:59:07.428Z", "path": "role/banner/execution/ears-specification.execution.md"}}, {"id": "requirements-analysis", "source": "project", "protocol": "execution", "name": "Requirements Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/banner/execution/requirements-analysis.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.428Z", "updatedAt": "2025-08-04T02:59:07.428Z", "scannedAt": "2025-08-04T02:59:07.428Z", "path": "role/banner/execution/requirements-analysis.execution.md"}}, {"id": "shrimp-task-integration", "source": "project", "protocol": "execution", "name": "Shrimp Task Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/shrimp-task-integration.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.443Z", "updatedAt": "2025-08-04T02:59:07.443Z", "scannedAt": "2025-08-04T02:59:07.443Z", "path": "role/stark/execution/shrimp-task-integration.execution.md"}}, {"id": "zhi-interaction-protocol", "source": "project", "protocol": "knowledge", "name": "Zhi Interaction Protocol 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/zhi-interaction-protocol.knowledge.md", "metadata": {"createdAt": "2025-08-04T02:59:07.446Z", "updatedAt": "2025-08-04T02:59:07.446Z", "scannedAt": "2025-08-04T02:59:07.446Z", "path": "role/vision/knowledge/zhi-interaction-protocol.knowledge.md"}}, {"id": "analytical-thinking", "source": "project", "protocol": "thought", "name": "Analytical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/banner/thought/analytical-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.429Z", "updatedAt": "2025-08-04T02:59:07.429Z", "scannedAt": "2025-08-04T02:59:07.429Z", "path": "role/banner/thought/analytical-thinking.thought.md"}}, {"id": "risk-assessment", "source": "project", "protocol": "thought", "name": "Risk Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/risk-assessment.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.432Z", "updatedAt": "2025-08-04T02:59:07.432Z", "scannedAt": "2025-08-04T02:59:07.432Z", "path": "role/black-widow/thought/risk-assessment.thought.md"}}, {"id": "black-widow", "source": "project", "protocol": "role", "name": "Black Widow 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/black-widow/black-widow.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.430Z", "updatedAt": "2025-08-04T02:59:07.430Z", "scannedAt": "2025-08-04T02:59:07.430Z", "path": "role/black-widow/black-widow.role.md"}}, {"id": "intelligence-workflow", "source": "project", "protocol": "execution", "name": "Intelligence Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/intelligence-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.431Z", "updatedAt": "2025-08-04T02:59:07.431Z", "scannedAt": "2025-08-04T02:59:07.431Z", "path": "role/black-widow/execution/intelligence-workflow.execution.md"}}, {"id": "interaction-and-results", "source": "project", "protocol": "execution", "name": "Interaction And Results 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/interaction-and-results.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.431Z", "updatedAt": "2025-08-04T02:59:07.431Z", "scannedAt": "2025-08-04T02:59:07.431Z", "path": "role/black-widow/execution/interaction-and-results.execution.md"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.442Z", "updatedAt": "2025-08-04T02:59:07.442Z", "scannedAt": "2025-08-04T02:59:07.442Z", "path": "role/pepper/execution/tool-orchestration.execution.md"}}, {"id": "intelligence-analysis", "source": "project", "protocol": "thought", "name": "Intelligence Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/intelligence-analysis.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.431Z", "updatedAt": "2025-08-04T02:59:07.431Z", "scannedAt": "2025-08-04T02:59:07.431Z", "path": "role/black-widow/thought/intelligence-analysis.thought.md"}}, {"id": "pattern-recognition", "source": "project", "protocol": "thought", "name": "Pattern Recognition 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/pattern-recognition.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.432Z", "updatedAt": "2025-08-04T02:59:07.432Z", "scannedAt": "2025-08-04T02:59:07.432Z", "path": "role/black-widow/thought/pattern-recognition.thought.md"}}, {"id": "dialogue-management", "source": "project", "protocol": "execution", "name": "Dialogue Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/dialogue-management.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.433Z", "updatedAt": "2025-08-04T02:59:07.433Z", "scannedAt": "2025-08-04T02:59:07.433Z", "path": "role/fury/execution/dialogue-management.execution.md"}}, {"id": "fury-workflow", "source": "project", "protocol": "execution", "name": "Fury Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/fury-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.433Z", "updatedAt": "2025-08-04T02:59:07.433Z", "scannedAt": "2025-08-04T02:59:07.433Z", "path": "role/fury/execution/fury-workflow.execution.md"}}, {"id": "resume-generation", "source": "project", "protocol": "execution", "name": "Resume Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/resume-generation.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.433Z", "updatedAt": "2025-08-04T02:59:07.433Z", "scannedAt": "2025-08-04T02:59:07.433Z", "path": "role/fury/execution/resume-generation.execution.md"}}, {"id": "fury", "source": "project", "protocol": "role", "name": "Fury 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fury/fury.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.434Z", "updatedAt": "2025-08-04T02:59:07.434Z", "scannedAt": "2025-08-04T02:59:07.434Z", "path": "role/fury/fury.role.md"}}, {"id": "agent-broker-mindset", "source": "project", "protocol": "thought", "name": "Agent Broker Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/agent-broker-mindset.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.434Z", "updatedAt": "2025-08-04T02:59:07.434Z", "scannedAt": "2025-08-04T02:59:07.434Z", "path": "role/fury/thought/agent-broker-mindset.thought.md"}}, {"id": "value-discovery-techniques", "source": "project", "protocol": "thought", "name": "Value Discovery Techniques 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/value-discovery-techniques.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.435Z", "updatedAt": "2025-08-04T02:59:07.435Z", "scannedAt": "2025-08-04T02:59:07.435Z", "path": "role/fury/thought/value-discovery-techniques.thought.md"}}, {"id": "maintenance-workflow", "source": "project", "protocol": "execution", "name": "Maintenance Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/hawkeye/execution/maintenance-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.435Z", "updatedAt": "2025-08-04T02:59:07.435Z", "scannedAt": "2025-08-04T02:59:07.435Z", "path": "role/hawkeye/execution/maintenance-workflow.execution.md"}}, {"id": "system-monitoring", "source": "project", "protocol": "execution", "name": "System Monitoring 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/hawkeye/execution/system-monitoring.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.436Z", "updatedAt": "2025-08-04T02:59:07.436Z", "scannedAt": "2025-08-04T02:59:07.436Z", "path": "role/hawkeye/execution/system-monitoring.execution.md"}}, {"id": "hawkeye", "source": "project", "protocol": "role", "name": "Hawkeye 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/hawkeye/hawkeye.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.436Z", "updatedAt": "2025-08-04T02:59:07.436Z", "scannedAt": "2025-08-04T02:59:07.436Z", "path": "role/hawkeye/hawkeye.role.md"}}, {"id": "maintenance-thinking", "source": "project", "protocol": "thought", "name": "Maintenance Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/hawkeye/thought/maintenance-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.437Z", "updatedAt": "2025-08-04T02:59:07.437Z", "scannedAt": "2025-08-04T02:59:07.437Z", "path": "role/hawkeye/thought/maintenance-thinking.thought.md"}}, {"id": "system-health-evaluation", "source": "project", "protocol": "thought", "name": "System Health Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/hawkeye/thought/system-health-evaluation.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.437Z", "updatedAt": "2025-08-04T02:59:07.437Z", "scannedAt": "2025-08-04T02:59:07.437Z", "path": "role/hawkeye/thought/system-health-evaluation.thought.md"}}, {"id": "code-implementation", "source": "project", "protocol": "execution", "name": "Code Implementation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/jarvis/execution/code-implementation.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.438Z", "updatedAt": "2025-08-04T02:59:07.438Z", "scannedAt": "2025-08-04T02:59:07.438Z", "path": "role/jarvis/execution/code-implementation.execution.md"}}, {"id": "testing-strategy", "source": "project", "protocol": "execution", "name": "Testing Strategy 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/jarvis/execution/testing-strategy.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.439Z", "updatedAt": "2025-08-04T02:59:07.439Z", "scannedAt": "2025-08-04T02:59:07.439Z", "path": "role/jarvis/execution/testing-strategy.execution.md"}}, {"id": "jarvis", "source": "project", "protocol": "role", "name": "Jarvis 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/jarvis/jarvis.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.439Z", "updatedAt": "2025-08-04T02:59:07.439Z", "scannedAt": "2025-08-04T02:59:07.439Z", "path": "role/jarvis/jarvis.role.md"}}, {"id": "code-quality-evaluation", "source": "project", "protocol": "thought", "name": "Code Quality Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/jarvis/thought/code-quality-evaluation.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.440Z", "updatedAt": "2025-08-04T02:59:07.440Z", "scannedAt": "2025-08-04T02:59:07.440Z", "path": "role/jarvis/thought/code-quality-evaluation.thought.md"}}, {"id": "development-thinking", "source": "project", "protocol": "thought", "name": "Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/jarvis/thought/development-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.440Z", "updatedAt": "2025-08-04T02:59:07.440Z", "scannedAt": "2025-08-04T02:59:07.440Z", "path": "role/jarvis/thought/development-thinking.thought.md"}}, {"id": "core-management", "source": "project", "protocol": "execution", "name": "Core Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/core-management.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.441Z", "updatedAt": "2025-08-04T02:59:07.441Z", "scannedAt": "2025-08-04T02:59:07.441Z", "path": "role/pepper/execution/core-management.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.442Z", "updatedAt": "2025-08-04T02:59:07.442Z", "scannedAt": "2025-08-04T02:59:07.442Z", "path": "role/pepper/pepper.role.md"}}, {"id": "adaptive-learning", "source": "project", "protocol": "thought", "name": "Adaptive Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/adaptive-learning.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.442Z", "updatedAt": "2025-08-04T02:59:07.442Z", "scannedAt": "2025-08-04T02:59:07.442Z", "path": "role/pepper/thought/adaptive-learning.thought.md"}}, {"id": "verification-mindset", "source": "project", "protocol": "thought", "name": "Verification Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/verification-mindset.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.442Z", "updatedAt": "2025-08-04T02:59:07.442Z", "scannedAt": "2025-08-04T02:59:07.442Z", "path": "role/pepper/thought/verification-mindset.thought.md"}}, {"id": "architecture-design", "source": "project", "protocol": "execution", "name": "Architecture Design 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/architecture-design.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.443Z", "updatedAt": "2025-08-04T02:59:07.443Z", "scannedAt": "2025-08-04T02:59:07.443Z", "path": "role/stark/execution/architecture-design.execution.md"}}, {"id": "technology-selection", "source": "project", "protocol": "execution", "name": "Technology Selection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/technology-selection.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.443Z", "updatedAt": "2025-08-04T02:59:07.443Z", "scannedAt": "2025-08-04T02:59:07.443Z", "path": "role/stark/execution/technology-selection.execution.md"}}, {"id": "stark", "source": "project", "protocol": "role", "name": "Stark 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/stark/stark.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.444Z", "updatedAt": "2025-08-04T02:59:07.444Z", "scannedAt": "2025-08-04T02:59:07.444Z", "path": "role/stark/stark.role.md"}}, {"id": "architectural-thinking", "source": "project", "protocol": "thought", "name": "Architectural Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stark/thought/architectural-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.444Z", "updatedAt": "2025-08-04T02:59:07.444Z", "scannedAt": "2025-08-04T02:59:07.444Z", "path": "role/stark/thought/architectural-thinking.thought.md"}}, {"id": "technology-evaluation", "source": "project", "protocol": "thought", "name": "Technology Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stark/thought/technology-evaluation.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.445Z", "updatedAt": "2025-08-04T02:59:07.445Z", "scannedAt": "2025-08-04T02:59:07.445Z", "path": "role/stark/thought/technology-evaluation.thought.md"}}, {"id": "vision-document-management", "source": "project", "protocol": "execution", "name": "Vision Document Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-document-management.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.445Z", "updatedAt": "2025-08-04T02:59:07.445Z", "scannedAt": "2025-08-04T02:59:07.445Z", "path": "role/vision/execution/vision-document-management.execution.md"}}, {"id": "vision-enhanced-task-workflow", "source": "project", "protocol": "execution", "name": "Vision Enhanced Task Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-enhanced-task-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T02:59:07.445Z", "updatedAt": "2025-08-04T02:59:07.445Z", "scannedAt": "2025-08-04T02:59:07.445Z", "path": "role/vision/execution/vision-enhanced-task-workflow.execution.md"}}, {"id": "shrimp-task-manager-tools", "source": "project", "protocol": "knowledge", "name": "Shrimp Task Manager Tools 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/shrimp-task-manager-tools.knowledge.md", "metadata": {"createdAt": "2025-08-04T02:59:07.446Z", "updatedAt": "2025-08-04T02:59:07.446Z", "scannedAt": "2025-08-04T02:59:07.446Z", "path": "role/vision/knowledge/shrimp-task-manager-tools.knowledge.md"}}, {"id": "vision-analytical-mind", "source": "project", "protocol": "thought", "name": "Vision Analytical Mind 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-analytical-mind.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.446Z", "updatedAt": "2025-08-04T02:59:07.446Z", "scannedAt": "2025-08-04T02:59:07.446Z", "path": "role/vision/thought/vision-analytical-mind.thought.md"}}, {"id": "vision-task-strategy", "source": "project", "protocol": "thought", "name": "Vision Task Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-task-strategy.thought.md", "metadata": {"createdAt": "2025-08-04T02:59:07.447Z", "updatedAt": "2025-08-04T02:59:07.447Z", "scannedAt": "2025-08-04T02:59:07.447Z", "path": "role/vision/thought/vision-task-strategy.thought.md"}}, {"id": "vision", "source": "project", "protocol": "role", "name": "Vision 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vision/vision.role.md", "metadata": {"createdAt": "2025-08-04T02:59:07.447Z", "updatedAt": "2025-08-04T02:59:07.447Z", "scannedAt": "2025-08-04T02:59:07.447Z", "path": "role/vision/vision.role.md"}}], "stats": {"totalResources": 44, "byProtocol": {"role": 8, "execution": 18, "knowledge": 2, "thought": 16}, "bySource": {"project": 44}}}